# DataTable Components

Shared DataTable components for consistent table UI across the application.

## Components

- `DataTable` - Main table component with built-in features
- `DataTableColumnHeader` - Sortable column headers
- `DataTablePagination` - Table pagination controls
- `DataTableToolbar` - Search and filter toolbar
- `DataTableFacetedFilter` - Multi-select filter dropdown
- `DataTableViewOptions` - Column visibility toggle

## Usage

```tsx
import { DataTable } from '@/components/ui/data-table'

// Basic usage
<DataTable 
  data={data} 
  columns={columns}
/>

// With search and filters
<DataTable 
  data={data} 
  columns={columns}
  searchKey="title"
  searchPlaceholder="Search items..."
  facetedFilters={[
    {
      columnKey: 'status',
      title: 'Status',
      options: statusOptions,
    },
    {
      columnKey: 'priority',
      title: 'Priority', 
      options: priorityOptions,
    },
  ]}
/>
```

## Props

### DataTable

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | `TData[]` | - | Table data array |
| `columns` | `ColumnDef<TData, TValue>[]` | - | Column definitions |
| `searchKey` | `string` | `'title'` | Column key for search |
| `searchPlaceholder` | `string` | `'Filter...'` | Search input placeholder |
| `facetedFilters` | `FacetedFilter[]` | `[]` | Filter configurations |

### FacetedFilter

| Prop | Type | Description |
|------|------|-------------|
| `columnKey` | `string` | Column key to filter |
| `title` | `string` | Filter display title |
| `options` | `FilterOption[]` | Available filter options |

### FilterOption

| Prop | Type | Description |
|------|------|-------------|
| `label` | `string` | Option display label |
| `value` | `string` | Option value |
| `icon` | `ComponentType` | Optional icon component |

## Column Meta Support

Supports `className` meta for custom styling:

```tsx
{
  accessorKey: 'username',
  header: ({ column }) => (
    <DataTableColumnHeader column={column} title='Username' />
  ),
  meta: {
    className: 'sticky left-0 bg-background'
  },
}
```

## Features

- ✅ Sorting
- ✅ Filtering (search + faceted)
- ✅ Pagination
- ✅ Row selection
- ✅ Column visibility toggle
- ✅ Sticky columns support
- ✅ TypeScript support
- ✅ Responsive design
